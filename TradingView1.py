#!/usr/bin/env python3
"""
TradingView1 - Clean executable that reads and executes output.bin
"""

import os
import sys
import subprocess
import tempfile
import shutil
from pathlib import Path

def get_resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    try:
        # <PERSON><PERSON><PERSON>nstalle<PERSON> creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    
    return os.path.join(base_path, relative_path)

def check_pe_format(bin_data):
    """Check if binary is a valid PE (Windows executable) format"""
    try:
        # Check DOS header
        if len(bin_data) < 64:
            return False, "File too small to be a valid executable"

        # Check DOS signature "MZ"
        if bin_data[0:2] != b'MZ':
            return False, "Not a valid DOS/Windows executable (missing MZ signature)"

        # Get PE header offset
        pe_offset = int.from_bytes(bin_data[60:64], 'little')

        if pe_offset >= len(bin_data) - 4:
            return False, "Invalid PE header offset"

        # Check PE signature "PE\0\0"
        if bin_data[pe_offset:pe_offset+4] != b'PE\0\0':
            return False, "Not a valid PE executable (missing PE signature)"

        # Check machine type (avoid 16-bit)
        machine_type = int.from_bytes(bin_data[pe_offset+4:pe_offset+6], 'little')

        # Common 32/64-bit machine types
        valid_machines = {
            0x014c: "i386 (32-bit)",
            0x8664: "x86-64 (64-bit)",
            0x01c0: "ARM",
            0xaa64: "ARM64"
        }

        if machine_type not in valid_machines:
            return False, f"Unsupported machine type: 0x{machine_type:04x} (possibly 16-bit or unknown format)"

        return True, f"Valid {valid_machines[machine_type]} executable"

    except Exception as e:
        return False, f"Error checking PE format: {e}"

def read_and_execute_bin():
    """Read output.bin and execute it"""
    try:
        # Get the path to output.bin
        bin_path = get_resource_path("output.bin")

        if not os.path.exists(bin_path):
            print(f"Error: output.bin not found at {bin_path}")
            return False

        # Read the binary file
        with open(bin_path, 'rb') as f:
            bin_data = f.read()

        print(f"Successfully read {len(bin_data)} bytes from output.bin")

        # Check if it's a valid PE executable
        is_valid, message = check_pe_format(bin_data)
        print(f"PE Format Check: {message}")

        if not is_valid:
            print("Warning: Binary may not be a valid Windows executable")
            print("Attempting to execute anyway...")

        # Create a temporary executable file
        with tempfile.NamedTemporaryFile(delete=False, suffix='.exe') as temp_file:
            temp_file.write(bin_data)
            temp_exe_path = temp_file.name

        # Make it executable (on Windows this is automatic)
        os.chmod(temp_exe_path, 0o755)

        print(f"Created temporary executable: {temp_exe_path}")

        # Execute the temporary file with different methods
        success = False

        # Method 1: Direct execution
        try:
            print("Attempting direct execution...")
            result = subprocess.run([temp_exe_path],
                                  capture_output=True,
                                  text=True,
                                  timeout=30,
                                  creationflags=subprocess.CREATE_NEW_CONSOLE if sys.platform == "win32" else 0)

            print(f"Execution completed with return code: {result.returncode}")
            if result.stdout:
                print(f"STDOUT: {result.stdout}")
            if result.stderr:
                print(f"STDERR: {result.stderr}")
            success = True

        except subprocess.TimeoutExpired:
            print("Direct execution timed out after 30 seconds")
        except Exception as e:
            print(f"Direct execution failed: {e}")

            # Method 2: Try with cmd.exe wrapper (for compatibility)
            if sys.platform == "win32":
                try:
                    print("Attempting execution via cmd.exe...")
                    result = subprocess.run(['cmd', '/c', temp_exe_path],
                                          capture_output=True,
                                          text=True,
                                          timeout=30)

                    print(f"CMD execution completed with return code: {result.returncode}")
                    if result.stdout:
                        print(f"STDOUT: {result.stdout}")
                    if result.stderr:
                        print(f"STDERR: {result.stderr}")
                    success = True

                except Exception as e2:
                    print(f"CMD execution also failed: {e2}")

        # Clean up temporary file
        try:
            os.unlink(temp_exe_path)
            print("Temporary file cleaned up")
        except:
            pass

        return success

    except Exception as e:
        print(f"Error reading or executing output.bin: {e}")
        return False

def safe_input(prompt="Press Enter to exit..."):
    """Safe input function that works with or without console"""
    try:
        if hasattr(sys, 'stdin') and sys.stdin:
            return input(prompt)
        else:
            # No console available, just wait a bit
            import time
            print(prompt)
            time.sleep(3)
            return ""
    except:
        # Fallback: just wait
        import time
        print(prompt)
        time.sleep(3)
        return ""

def main():
    """Main function"""
    # Create log file for debugging
    log_file = "TradingView1_log.txt"

    try:
        with open(log_file, 'w') as f:
            f.write("TradingView1 - Starting...\n")
            f.write("=" * 50 + "\n")

        print("TradingView1 - Starting...")
        print("=" * 50)

        # Check if running as executable or script
        if getattr(sys, 'frozen', False):
            print("Running as compiled executable")
            with open(log_file, 'a') as f:
                f.write("Running as compiled executable\n")
        else:
            print("Running as Python script")
            with open(log_file, 'a') as f:
                f.write("Running as Python script\n")

        # Execute the binary
        success = read_and_execute_bin()

        if success:
            print("=" * 50)
            print("TradingView1 - Completed successfully")
            with open(log_file, 'a') as f:
                f.write("=" * 50 + "\n")
                f.write("TradingView1 - Completed successfully\n")
        else:
            print("=" * 50)
            print("TradingView1 - Completed with errors")
            with open(log_file, 'a') as f:
                f.write("=" * 50 + "\n")
                f.write("TradingView1 - Completed with errors\n")

        # Keep console open for a moment (safe version)
        safe_input("Press Enter to exit...")

        if not success:
            sys.exit(1)

    except Exception as e:
        error_msg = f"Fatal error in main: {e}"
        print(error_msg)
        try:
            with open(log_file, 'a') as f:
                f.write(f"FATAL ERROR: {error_msg}\n")
        except:
            pass
        sys.exit(1)

if __name__ == "__main__":
    main()
