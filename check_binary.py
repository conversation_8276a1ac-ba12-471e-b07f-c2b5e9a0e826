#!/usr/bin/env python3
"""
Binary file format checker
Checks if binary files are valid Windows executables
"""

import os
import sys

def check_pe_format(file_path):
    """Check if binary is a valid PE (Windows executable) format"""
    try:
        with open(file_path, 'rb') as f:
            bin_data = f.read()
        
        print(f"File: {file_path}")
        print(f"Size: {len(bin_data)} bytes")
        
        # Check DOS header
        if len(bin_data) < 64:
            return False, "File too small to be a valid executable"
        
        # Check DOS signature "MZ"
        dos_sig = bin_data[0:2]
        print(f"DOS signature: {dos_sig} (expected: b'MZ')")
        
        if dos_sig != b'MZ':
            # Check if it might be a different format
            if bin_data[0:4] == b'\x7fELF':
                return False, "This is a Linux ELF executable, not Windows PE"
            elif bin_data[0:4] == b'\xca\xfe\xba\xbe':
                return False, "This is a Mach-O executable (macOS), not Windows PE"
            elif bin_data[0:2] == b'\x4d\x5a':  # Alternative check
                print("Found MZ signature in alternative encoding")
            else:
                return False, f"Not a valid DOS/Windows executable (found: {dos_sig.hex()})"
        
        # Get PE header offset
        if len(bin_data) < 64:
            return False, "File too small for PE header offset"
            
        pe_offset = int.from_bytes(bin_data[60:64], 'little')
        print(f"PE header offset: 0x{pe_offset:x}")
        
        if pe_offset >= len(bin_data) - 4:
            return False, f"Invalid PE header offset: 0x{pe_offset:x} (file size: {len(bin_data)})"
        
        # Check PE signature "PE\0\0"
        pe_sig = bin_data[pe_offset:pe_offset+4]
        print(f"PE signature: {pe_sig} (expected: b'PE\\x00\\x00')")
        
        if pe_sig != b'PE\0\0':
            return False, f"Not a valid PE executable (found: {pe_sig.hex()})"
        
        # Check machine type (avoid 16-bit)
        machine_type = int.from_bytes(bin_data[pe_offset+4:pe_offset+6], 'little')
        print(f"Machine type: 0x{machine_type:04x}")
        
        # Common machine types
        machine_types = {
            0x014c: "i386 (32-bit Intel)",
            0x8664: "x86-64 (64-bit Intel/AMD)",
            0x01c0: "ARM (32-bit)",
            0xaa64: "ARM64 (64-bit ARM)",
            0x0162: "MIPS R3000",
            0x0166: "MIPS R4000",
            0x0168: "MIPS R10000",
            0x0169: "MIPS WCE v2",
            0x0184: "Alpha AXP",
            0x01a2: "Hitachi SH3",
            0x01a6: "Hitachi SH4",
            0x01c2: "ARM Thumb",
            0x01c4: "ARM Thumb-2",
            0x01f0: "PowerPC",
            0x01f1: "PowerPC with FPU",
            0x0200: "Intel Itanium",
            0x0266: "MIPS16",
            0x0366: "MIPS with FPU",
            0x0466: "MIPS16 with FPU",
            0x0ebc: "EFI Byte Code"
        }
        
        if machine_type in machine_types:
            machine_desc = machine_types[machine_type]
            print(f"Machine description: {machine_desc}")
            
            # Check for 16-bit (not supported on modern Windows)
            if machine_type in [0x014d]:  # i860, old 16-bit
                return False, f"16-bit executable detected (0x{machine_type:04x}). Not supported on modern Windows."
            
            return True, f"Valid {machine_desc} executable"
        else:
            return False, f"Unknown/unsupported machine type: 0x{machine_type:04x}"
        
    except Exception as e:
        return False, f"Error checking PE format: {e}"

def main():
    """Main function"""
    print("Binary File Format Checker")
    print("=" * 50)
    
    # Check both binary files
    files_to_check = ["output.bin", "payload.bin"]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"\nChecking {file_path}:")
            print("-" * 30)
            
            is_valid, message = check_pe_format(file_path)
            
            if is_valid:
                print(f"✓ VALID: {message}")
            else:
                print(f"✗ INVALID: {message}")
                
            print()
        else:
            print(f"\n✗ File not found: {file_path}")
    
    print("=" * 50)
    print("Check completed.")
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
